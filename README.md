# Mail Aggregator (Next.js + Keycloak + IMAP)

This app demonstrates:

- Keycloak login on the client (keycloak-js) with role check (`mail-reader` by default).
- Route Handlers as a Backend-for-Frontend to read Gmail via IMAP (imapflow) and parse messages (mailparser). This is aligned with Next.js guidance to use Route Handlers for public HTTP endpoints that return JSON [^1].
- Read-only mailbox access. No writes, no marking as seen.

## Required environment variables

Client (public):
- NEXT_PUBLIC_KEYCLOAK_URL=https://keycloak.example.com
- NEXT_PUBLIC_KEYCLOAK_REALM=your-realm
- NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=mail-aggregator

Server:
- KEYCLOAK_ISSUER_URL=https://keycloak.example.com/realms/your-realm
- KEYCLOAK_CLIENT_ID=mail-aggregator (optional if same as public client ID)
- K<PERSON><PERSON><PERSON>OAK_REQUIRED_ROLE=mail-reader
- GMAIL_IMAP_HOST=imap.gmail.com
- GMAIL_IMAP_PORT=993
- GMAIL_IMAP_USER=<EMAIL>
- GMAIL_IMAP_PASSWORD=your_app_specific_password

## Notes

- Pagination is implemented by retrieving the UID list and slicing in reverse order. IMAP has no offset-based pagination; this approach is acceptable for personal usage. For large mailboxes or heavy filtering, consider server-side IMAP search and/or a Postgres cache.
- Recipient filtering inspects To/Cc/Bcc and headers Delivered-To / X-Forwarded-To to include Cloudflare Email Routing targets.

[^1]: Next.js Guide "Backend for Frontend": Route Handlers are public HTTP endpoints that can return any content type; use them to manipulate data and hide backend details from the UI.
