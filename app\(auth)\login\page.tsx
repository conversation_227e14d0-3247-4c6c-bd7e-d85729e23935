"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShieldAlert } from "lucide-react";
import { useAuth } from "@/components/auth-provider";

export default function LoginPage() {
  const router = useRouter();
  const {
    authenticated,
    initializing,
    login,
    hasRequiredRole,
    requiredRole,
    configOk,
  } = useAuth();

  useEffect(() => {
    if (authenticated && hasRequiredRole) {
      router.replace("/");
    } else if (authenticated && !hasRequiredRole) {
      router.replace("/auth/signin?error=no_role");
    }
  }, [authenticated, hasRequiredRole, router]);

  // Do NOT auto-redirect; only redirect when user clicks the button
  // Keeping this effect empty ensures no automatic login happens on /login
  useEffect(() => {
    // no-op
  }, []);

  return (
    <main className="min-h-[calc(100vh-2rem)] flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {!configOk && (
          <Alert
            variant="destructive"
            className="mb-4"
          >
            <ShieldAlert className="h-4 w-4" />
            <AlertTitle>Missing configuration</AlertTitle>
            <AlertDescription>
              Keycloak environment variables are not set. Please configure
              authentication.
            </AlertDescription>
          </Alert>
        )}
        <Card>
          <CardHeader>
            <CardTitle>Login</CardTitle>
            <CardDescription>
              Sign in via Keycloak to access your aggregated inbox. Required
              role: <strong>{requiredRole}</strong>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {initializing ? (
              <div className="text-sm text-muted-foreground">
                Initializing...
              </div>
            ) : authenticated ? (
              hasRequiredRole ? (
                <div className="text-sm text-green-600">
                  Authenticated. Redirecting to inbox...
                </div>
              ) : (
                <Alert variant="destructive">
                  <ShieldAlert className="h-4 w-4" />
                  <AlertTitle>Access denied</AlertTitle>
                  <AlertDescription>
                    Your account is missing the required role. Contact admin.
                  </AlertDescription>
                </Alert>
              )
            ) : (
              <div className="grid gap-2">
                <Button
                  className="w-full"
                  onClick={() => login()}
                  disabled={!configOk}
                >
                  Continue with Keycloak
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
