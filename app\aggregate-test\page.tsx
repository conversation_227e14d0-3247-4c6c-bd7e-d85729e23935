"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "../lib/auth-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ArrowLeft,
  Search,
  Globe,
  Check,
  X,
  Loader2,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { apiGet, apiPost } from "../lib/api";

interface TokenModelTestResult {
  modelName: string;
  lastTestSuccess?: boolean;
  lastTestDuration?: number;
  lastTestAt?: string;
}

interface TokenRecord {
  name: string;
  type: "Chat API" | "Claude Code";
  urlPath?: string;
  apiPath?: string;
  token?: string;
  modelList?: string[];
  note?: string;
  postUrl?: string;
  status?: "active" | "failed" | "not tested" | "test again";
  canFetch?: boolean;
  lastTestSuccess?: boolean;
  lastTestDuration?: number;
  lastTestAt?: string;
  modelTestResults?: TokenModelTestResult[];
}

interface ModelGroup {
  model: string;
  records: TokenRecord[];
}

interface TestResult {
  success: boolean;
  response: string;
  error?: string;
  firstByteTime?: number;
  totalTime?: number;
}

export default function AggregateTestPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  const [tokens, setTokens] = useState<TokenRecord[]>([]);
  const [modelGroups, setModelGroups] = useState<ModelGroup[]>([]);
  const [allModels, setAllModels] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedRecords, setSelectedRecords] = useState<string[]>([]);
  const [isTestLoading, setIsTestLoading] = useState(false);
  const [testResults, setTestResults] = useState<{
    [recordName: string]: TestResult;
  }>({});
  const [currentTestingRecord, setCurrentTestingRecord] = useState<string>("");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get model test status for badge styling
  const getModelTestStatus = (token: TokenRecord, modelName: string) => {
    const modelResult = token.modelTestResults?.find(
      (result) => result.modelName === modelName
    );
    return modelResult?.lastTestSuccess;
  };

  // Fetch tokens
  useEffect(() => {
    if (user) {
      fetchTokens();
    }
  }, [user]);

  // Group tokens by model and collect all models
  useEffect(() => {
    if (!Array.isArray(tokens)) {
      setModelGroups([]);
      setAllModels([]);
      return;
    }

    const groups: { [model: string]: TokenRecord[] } = {};
    const modelSet = new Set<string>();

    tokens.forEach((token) => {
      if (token.modelList && Array.isArray(token.modelList)) {
        token.modelList.forEach((model) => {
          modelSet.add(model);
          if (!groups[model]) {
            groups[model] = [];
          }
          groups[model].push(token);
        });
      }
    });

    const modelGroupsArray = Object.entries(groups)
      .map(([model, records]) => ({ model, records }))
      .sort((a, b) => a.model.localeCompare(b.model));

    const allModelsArray = Array.from(modelSet).sort();

    console.log("Model groups:", modelGroupsArray);
    console.log("All models:", allModelsArray);

    setModelGroups(modelGroupsArray);
    setAllModels(allModelsArray);
  }, [tokens]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsModelDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchTokens = async () => {
    try {
      const response = await apiGet("/api/tokens");
      const data = await response.json();
      console.log("Fetched tokens:", data);
      setTokens(data);
    } catch (error) {
      console.error("Failed to fetch tokens:", error);
    }
  };

  const recordTestResult = async (
    tokenName: string,
    modelName: string,
    success: boolean,
    durationMs: number,
    errorMessage?: string
  ) => {
    try {
      await apiPost("/api/test-history", {
        tokenName,
        modelName,
        testType: "aggregate",
        success,
        durationMs,
        errorMessage,
      });
    } catch (error) {
      console.error("Failed to record test result:", error);
    }
  };

  const filteredModels = allModels.filter((model) =>
    model.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleRecordSelection = (recordName: string) => {
    setSelectedRecords((prev) =>
      prev.includes(recordName)
        ? prev.filter((name) => name !== recordName)
        : [...prev, recordName]
    );
  };

  const selectAllRecordsForModel = (model: string) => {
    const group = modelGroups.find((g) => g.model === model);
    if (group) {
      const recordNames = group.records.map((r) => r.name);
      setSelectedRecords(recordNames);
    }
  };

  const handleModelSelect = (model: string) => {
    setSelectedModel(model);
    setSelectedRecords([]);
    setTestResults({});
    setIsModelDropdownOpen(false);
    setSearchTerm(model); // Set search term to selected model
  };

  const runAggregateTest = async () => {
    if (!selectedModel || selectedRecords.length === 0) {
      return;
    }

    setIsTestLoading(true);
    setTestResults({});

    const selectedTokens = tokens.filter((token) =>
      selectedRecords.includes(token.name)
    );

    for (const token of selectedTokens) {
      setCurrentTestingRecord(token.name);

      const startTime = performance.now();
      let firstByteTime: number | undefined;
      let totalTime: number | undefined;

      try {
        // Construct the API URL based on token configuration
        let apiUrl = "/api/chat"; // Default fallback

        if (token.postUrl) {
          apiUrl = token.postUrl;
        } else if (token.urlPath) {
          const baseUrl = token.urlPath.replace(/\/+$/, "");
          apiUrl = `${baseUrl}/v1/chat/completions`;
        } else if (token.apiPath) {
          apiUrl = token.apiPath;
        }

        const headers: Record<string, string> = {
          "Content-Type": "application/json",
        };

        if (token.token) {
          headers["Authorization"] = `Bearer ${token.token}`;
        }

        const requestBody = {
          model: selectedModel,
          messages: [{ role: "user", content: "你好" }],
        };

        const response = await fetch(apiUrl, {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody),
        });

        firstByteTime = performance.now() - startTime;

        if (!response.ok) {
          const errorText = await response.text();
          totalTime = performance.now() - startTime;
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText };
          }
          throw new Error(
            errorData.error || `HTTP ${response.status}: ${response.statusText}`
          );
        }

        const data = await response.json();
        totalTime = performance.now() - startTime;

        setTestResults((prev) => ({
          ...prev,
          [token.name]: {
            success: true,
            response: JSON.stringify(data, null, 2),
            firstByteTime,
            totalTime,
          },
        }));

        // Record successful test result
        await recordTestResult(token.name, selectedModel, true, totalTime || 0);

        // Update token's last test result
        try {
          await apiPost("/api/tokens/update-test-result", {
            tokenName: token.name,
            success: true,
            durationMs: totalTime || 0,
          });
        } catch (error) {
          console.error("Failed to update token test result:", error);
        }
      } catch (err) {
        totalTime = performance.now() - startTime;
        const errorMessage = err instanceof Error ? err.message : "Test failed";
        setTestResults((prev) => ({
          ...prev,
          [token.name]: {
            success: false,
            response: "",
            error: errorMessage,
            firstByteTime,
            totalTime,
          },
        }));

        // Record failed test result
        await recordTestResult(
          token.name,
          selectedModel,
          false,
          totalTime || 0,
          errorMessage
        );

        // Update token's last test result
        try {
          await apiPost("/api/tokens/update-test-result", {
            tokenName: token.name,
            success: false,
            durationMs: totalTime || 0,
          });
        } catch (error) {
          console.error("Failed to update token test result:", error);
        }
      }
    }

    setCurrentTestingRecord("");
    setIsTestLoading(false);

    // Refresh tokens to show updated test results
    await fetchTokens();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push("/");
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header - Mobile optimized */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            className="w-fit"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          <h1 className="text-2xl sm:text-3xl font-bold">
            Aggregate Model Testing
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - Model Selection */}
          <div className="space-y-6">
            {/* Model Search & Selection */}
            <Card>
              <CardHeader>
                <CardTitle>
                  Select Model ({allModels.length} available)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="relative"
                  ref={dropdownRef}
                >
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search and select a model..."
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setIsModelDropdownOpen(true);
                    }}
                    onFocus={() => setIsModelDropdownOpen(true)}
                    className="pl-10"
                  />
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />

                  {/* Dropdown */}
                  {isModelDropdownOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-64 overflow-y-auto">
                      {filteredModels.length > 0 ? (
                        filteredModels.map((model) => {
                          const group = modelGroups.find(
                            (g) => g.model === model
                          );
                          return (
                            <div
                              key={model}
                              className={`flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 border-b last:border-b-0 ${
                                selectedModel === model ? "bg-blue-50" : ""
                              }`}
                              onClick={() => handleModelSelect(model)}
                            >
                              <div className="flex items-center gap-3">
                                <span className="font-medium">{model}</span>
                                <Badge variant="secondary">
                                  {group?.records.length || 0} records
                                </Badge>
                              </div>
                              {selectedModel === model && (
                                <Check className="w-4 h-4 text-blue-600" />
                              )}
                            </div>
                          );
                        })
                      ) : (
                        <div className="p-3 text-gray-500 text-center">
                          {allModels.length === 0
                            ? `No models available (${tokens.length} tokens loaded)`
                            : `No models match "${searchTerm}" (${allModels.length} total models)`}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Selected Model Display */}
                {selectedModel && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-blue-800">
                          Selected:
                        </span>
                        <span className="text-blue-600">{selectedModel}</span>
                        <Badge variant="secondary">
                          {modelGroups.find((g) => g.model === selectedModel)
                            ?.records.length || 0}{" "}
                          records
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedModel("");
                          setSearchTerm("");
                          setSelectedRecords([]);
                          setTestResults({});
                        }}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Record Selection & Testing */}
          <div className="space-y-6">
            {selectedModel && (
              <>
                {/* Record Selection */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Records with {selectedModel}</CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => selectAllRecordsForModel(selectedModel)}
                      >
                        Select All
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {modelGroups
                        .find((g) => g.model === selectedModel)
                        ?.records.map((record) => (
                          <div
                            key={record.name}
                            className={`flex items-center justify-between p-3 border rounded cursor-pointer hover:bg-gray-50 ${
                              selectedRecords.includes(record.name)
                                ? "bg-blue-50 border-blue-200"
                                : ""
                            }`}
                            onClick={() => toggleRecordSelection(record.name)}
                          >
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {record.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {record.type}
                              </div>
                              {/* Model Test Result */}
                              {selectedModel &&
                                (() => {
                                  const testStatus = getModelTestStatus(
                                    record,
                                    selectedModel
                                  );
                                  const modelResult =
                                    record.modelTestResults?.find(
                                      (result) =>
                                        result.modelName === selectedModel
                                    );

                                  if (testStatus !== undefined) {
                                    return (
                                      <div className="mt-1 flex items-center gap-2">
                                        <Badge
                                          variant="secondary"
                                          className={`text-xs ${
                                            testStatus
                                              ? "bg-green-100 text-green-800 border-green-200"
                                              : "bg-red-100 text-red-800 border-red-200"
                                          }`}
                                        >
                                          {testStatus ? "Success" : "Failed"}
                                        </Badge>
                                        {testStatus &&
                                          modelResult?.lastTestDuration && (
                                            <span className="text-xs text-gray-500">
                                              {(
                                                modelResult.lastTestDuration /
                                                1000
                                              ).toFixed(2)}
                                              s
                                            </span>
                                          )}
                                      </div>
                                    );
                                  }
                                  return null;
                                })()}
                            </div>
                            {selectedRecords.includes(record.name) && (
                              <Check className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Test Button */}
                {selectedRecords.length > 0 && (
                  <Card>
                    <CardContent className="pt-6">
                      <Button
                        onClick={runAggregateTest}
                        disabled={isTestLoading}
                        className="w-full"
                        size="lg"
                      >
                        {isTestLoading ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            {currentTestingRecord
                              ? `Testing ${currentTestingRecord}...`
                              : "Testing..."}
                          </>
                        ) : (
                          <>
                            <Globe className="w-4 h-4 mr-2" />
                            Test {selectedRecords.length} Records with{" "}
                            {selectedModel}
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Test Results for {selectedModel}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(testResults).map(([recordName, result]) => (
                  <div
                    key={recordName}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-3">
                      <h4 className="font-medium truncate">{recordName}</h4>
                      <Badge
                        variant={result.success ? "default" : "destructive"}
                        className="w-fit"
                      >
                        {result.success ? "Success" : "Failed"}
                      </Badge>
                    </div>

                    {/* Response Time Information */}
                    {(result.firstByteTime !== undefined ||
                      result.totalTime !== undefined) && (
                      <div className="bg-gray-50 p-3 rounded mb-3 grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label className="font-medium text-gray-700">
                            First Byte Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.firstByteTime !== undefined
                              ? `${(result.firstByteTime / 1000).toFixed(2)}s`
                              : "N/A"}
                          </p>
                        </div>
                        <div>
                          <Label className="font-medium text-gray-700">
                            Total Response Time:
                          </Label>
                          <p className="text-blue-600 font-mono">
                            {result.totalTime !== undefined
                              ? `${(result.totalTime / 1000).toFixed(2)}s`
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    )}

                    {result.success ? (
                      <Textarea
                        value={result.response}
                        readOnly
                        className="min-h-[150px] font-mono text-sm text-green-600 border-green-300"
                      />
                    ) : (
                      <div className="bg-red-50 p-3 rounded border border-red-200">
                        <p className="text-red-600 text-sm font-mono">
                          {result.error}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
