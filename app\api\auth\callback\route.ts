import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get("code")

  // 统一定义baseUrl，避免重复定义
  const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'

  // Debug logging
  console.log('=== CALLBACK DEBUG INFO ===')
  console.log('request.url:', request.url)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('request.headers.host:', request.headers.get('host'))
  console.log('request.headers.x-forwarded-host:', request.headers.get('x-forwarded-host'))
  console.log('request.headers.x-forwarded-proto:', request.headers.get('x-forwarded-proto'))
  console.log('searchParams:', Object.fromEntries(searchParams.entries()))
  console.log('code:', code)
  console.log('NEXTAUTH_URL env var:', process.env.NEXTAUTH_URL)
  console.log('NODE_ENV:', process.env.NODE_ENV)
  console.log('Final baseUrl used:', baseUrl)

  if (!code) {
    const redirectUrl = `${baseUrl}/?error=no_code`
    console.log('No code found, redirecting to:', redirectUrl)
    console.log('========================')
    return NextResponse.redirect(redirectUrl)
  }

  // Get the stored code verifier
  const cookieStore = cookies()
  const codeVerifierCookie = cookieStore.get("pkce_code_verifier")

  if (!codeVerifierCookie) {
    const redirectUrl = `${baseUrl}/?error=missing_code_verifier`
    console.log('No code verifier found, redirecting to:', redirectUrl)
    console.log('========================')
    return NextResponse.redirect(redirectUrl)
  }

  try {
    const redirectUri = `${baseUrl}/api/auth/callback`
    const tokenData = await KeycloakService.exchangeCodeForToken(code, redirectUri, codeVerifierCookie.value)
    const userInfo = await KeycloakService.getUserInfo(tokenData.access_token)

    // Clear the code verifier cookie
    cookieStore.delete("pkce_code_verifier")

    // Set secure cookies
    cookieStore.set("access_token", tokenData.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    cookieStore.set("refresh_token", tokenData.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.refresh_expires_in,
    })

    cookieStore.set("user_info", JSON.stringify(userInfo), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    const successUrl = `${baseUrl}/`
    console.log('Auth success, redirecting to:', successUrl)
    console.log('========================')
    return NextResponse.redirect(successUrl)
  } catch (error) {
    console.error("Auth callback error:", error)
    const errorUrl = `${baseUrl}/?error=auth_failed`
    console.log('Auth failed, redirecting to:', errorUrl)
    console.log('========================')
    return NextResponse.redirect(errorUrl)
  }
}
