import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  // 强制使用环境变量，不依赖request.nextUrl.origin
  const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
  const redirectUri = `${baseUrl}/api/auth/callback`

  // Enhanced debug logging
  console.log('=== LOGIN DEBUG INFO ===')
  console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)
  console.log('request.nextUrl.origin:', request.nextUrl.origin)
  console.log('request.url:', request.url)
  console.log('Final baseUrl:', baseUrl)
  console.log('Final redirectUri:', redirectUri)
  console.log('KEYCLOAK_URL:', process.env.KEYCLOAK_URL)
  console.log('KEYCLOAK_REALM:', process.env.KEYCLOAK_REALM)
  console.log('KEYCLOAK_CLIENT_ID:', process.env.KEYCLOAK_CLIENT_ID)

  // Generate PKCE parameters
  const { url: loginUrl, codeVerifier } = await KeycloakService.getLoginUrl(redirectUri)

  // Store code verifier in a secure cookie for the callback
  const cookieStore = cookies()
  cookieStore.set("pkce_code_verifier", codeVerifier, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 600, // 10 minutes
  })

  console.log('Generated loginUrl:', loginUrl)
  console.log('Code verifier stored in cookie')
  console.log('========================')

  // 正常重定向到Keycloak
  return NextResponse.redirect(loginUrl)
}
