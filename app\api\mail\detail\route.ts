import type { NextRequest } from "next/server"
import { getServerEnv } from "@/lib/config"
import { ensureRole, verifyAuth } from "@/lib/auth"
import { getMessageDetail, withImapClient } from "@/lib/imap"

export async function GET(req: NextRequest) {
  try {
    const auth = await verifyAuth(req)
    const { requiredRole, imap } = getServerEnv()
    ensureRole(auth, requiredRole)

    const { searchParams } = new URL(req.url)
    const uid = Number(searchParams.get("uid"))
    if (!uid) {
      return new Response("Missing uid", { status: 400 })
    }

    if (!imap.user || !imap.pass) {
      return new Response("IMAP not configured", { status: 503 })
    }

    const detail = await withImapClient(imap, async (client) => {
      return await getMessageDetail(client, uid)
    })

    return Response.json(detail)
  } catch (err: any) {
    const status = Number(err?.status) || 500
    const message = typeof err?.message === "string" ? err.message : "Internal Server Error"
    return new Response(message, { status })
  }
}
