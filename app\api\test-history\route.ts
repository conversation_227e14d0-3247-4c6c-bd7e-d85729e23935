import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { TestHistoryService } from "@/app/lib/db"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function GET(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const { searchParams } = new URL(request.url)
    const tokenName = searchParams.get('tokenName')
    const limit = parseInt(searchParams.get('limit') || '50')
    const statsOnly = searchParams.get('statsOnly') === 'true'

    if (statsOnly) {
      const stats = TestHistoryService.getTestStats(user.id, tokenName || undefined)
      return NextResponse.json(stats)
    }

    const history = TestHistoryService.getTestHistory(user.id, tokenName || undefined, limit)
    const stats = TestHistoryService.getTestStats(user.id, tokenName || undefined)

    return NextResponse.json({
      history,
      stats
    })
  } catch (error) {
    console.error("Failed to fetch test history:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const data = await request.json()
    const { tokenName, modelName, testType, success, durationMs, errorMessage } = data

    if (!tokenName || !modelName || !testType || typeof success !== 'boolean' || typeof durationMs !== 'number') {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const id = TestHistoryService.createTestRecord({
      tokenName,
      modelName,
      testType,
      success,
      durationMs,
      errorMessage,
      userId: user.id
    })

    return NextResponse.json({ id, success: true })
  } catch (error) {
    console.error("Failed to create test record:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
