import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { TokenService } from "@/app/lib/db"

async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const data = await request.json()
    const { tokenName, success, durationMs } = data

    if (!tokenName || typeof success !== 'boolean' || typeof durationMs !== 'number') {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const updated = TokenService.updateLastTestResult(tokenName, success, durationMs, user.id)
    
    if (!updated) {
      return NextResponse.json({ error: "Token not found or update failed" }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to update token test result:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
