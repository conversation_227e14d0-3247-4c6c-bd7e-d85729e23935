"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth-provider";

export default function CallbackPage() {
  const router = useRouter();
  const { authenticated, hasRequiredRole, initializing } = useAuth();

  useEffect(() => {
    if (!initializing) {
      if (authenticated && hasRequiredRole) {
        router.replace("/");
      } else if (authenticated && !hasRequiredRole) {
        router.replace("/auth/signin?error=no_role");
      } else {
        // Not authenticated, redirect to login
        router.replace("/login");
      }
    }
  }, [authenticated, hasRequiredRole, initializing, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="text-lg font-medium">Processing authentication...</div>
        <div className="text-sm text-muted-foreground mt-2">
          Please wait while we complete your login.
        </div>
      </div>
    </div>
  );
}
