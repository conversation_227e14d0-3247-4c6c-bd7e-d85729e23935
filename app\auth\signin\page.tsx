"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShieldAlert } from "lucide-react";
import { useAuth } from "@/components/auth-provider";

export default function SignInPage() {
  const router = useRouter();
  const params = useSearchParams();
  const error = params.get("error");
  const { authenticated, hasRequiredRole, initializing, login, requiredRole } =
    useAuth();

  useEffect(() => {
    if (authenticated && hasRequiredRole) {
      router.replace("/");
    }
  }, [authenticated, hasRequiredRole, router]);

  return (
    <main className="min-h-[calc(100vh-2rem)] flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {error === "no_role" && (
          <Alert
            variant="destructive"
            className="mb-4"
          >
            <ShieldAlert className="h-4 w-4" />
            <AlertTitle>Access denied</AlertTitle>
            <AlertDescription>
              Your account is missing the required role{" "}
              <strong>{requiredRole}</strong>.
            </AlertDescription>
          </Alert>
        )}
        <Card>
          <CardHeader>
            <CardTitle>Sign in</CardTitle>
            <CardDescription>
              Continue with Keycloak to access One Mail.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {initializing ? (
              <div className="text-sm text-muted-foreground">
                Initializing...
              </div>
            ) : (
              <div className="grid gap-2">
                <Button
                  className="w-full"
                  onClick={() => login()}
                >
                  Continue with Keycloak
                </Button>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => router.push("/login")}
                >
                  Go to classic login page
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
