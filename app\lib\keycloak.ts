export interface KeycloakUser {
  id: string
  name: string
  email: string
  preferred_username: string
}

export class KeycloakService {
  private static readonly KEYCLOAK_URL = process.env.KEYCLOAK_URL!
  private static readonly REALM = process.env.KEYCLOAK_REALM!
  private static readonly CLIENT_ID = process.env.KEYCLOAK_CLIENT_ID!
  private static readonly CLIENT_SECRET = process.env.KEYCLOAK_CLIENT_SECRET!

  // Generate PKCE code verifier and challenge
  private static generateCodeVerifier(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  private static async generateCodeChallenge(verifier: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(verifier)
    const digest = await crypto.subtle.digest('SHA-256', data)
    return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(digest))))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  static async getLoginUrl(redirectUri: string): Promise<{ url: string, codeVerifier: string }> {
    const codeVerifier = this.generateCodeVerifier()
    const codeChallenge = await this.generateCodeChallenge(codeVerifier)

    const params = new URLSearchParams({
      client_id: this.CLIENT_ID,
      redirect_uri: redirectUri,
      response_type: "code",
      scope: "openid profile email",
      code_challenge_method: "S256",
      code_challenge: codeChallenge,
    })

    return {
      url: `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/auth?${params}`,
      codeVerifier
    }
  }

  static async exchangeCodeForToken(code: string, redirectUri: string, codeVerifier: string) {
    const tokenUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/token`

    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to exchange code for token")
    }

    return response.json()
  }

  static async getUserInfo(accessToken: string): Promise<KeycloakUser> {
    const userInfoUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/userinfo`

    const response = await fetch(userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error("Failed to get user info")
    }

    const userInfo = await response.json()

    return {
      id: userInfo.sub,
      name: userInfo.name || userInfo.preferred_username,
      email: userInfo.email,
      preferred_username: userInfo.preferred_username,
    }
  }

  static async logout(refreshToken: string): Promise<void> {
    const logoutUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/logout`

    await fetch(logoutUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        refresh_token: refreshToken,
      }),
    })
  }
}
