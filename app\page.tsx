import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function HomePage() {
  return (
    <main className="min-h-[calc(100vh-2rem)] flex items-center justify-center p-4">
      <div className="mx-auto max-w-4xl grid md:grid-cols-2 gap-8 items-center">
        <div className="space-y-4">
          <h1 className="text-3xl font-semibold">Mail Aggregator</h1>
          <p className="text-muted-foreground">
            A personal IMAP mail aggregator for Gmail with Keycloak login and
            role-based access.
          </p>
          <div className="flex gap-3">
            <Button asChild>
              <Link href="/login">Login</Link>
            </Button>
            <Button
              asChild
              variant="outline"
            >
              <a
                href="https://mail.google.com/"
                target="_blank"
                rel="noreferrer"
              >
                Gmail
              </a>
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            {"IMAP access is read-only. No changes are made to your mailbox."}
          </p>
        </div>
        <div className="relative aspect-video rounded-lg overflow-hidden border bg-muted">
          <Image
            src="/mail-list-ui.png"
            alt="Inbox preview"
            fill
            className="object-cover"
          />
        </div>
      </div>
    </main>
  );
}
