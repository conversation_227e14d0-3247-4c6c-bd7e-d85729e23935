"use client";

import Keycloak, {
  type KeycloakConfig,
  type KeycloakProfile,
} from "keycloak-js";
import type React from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

type AuthContextType = {
  keycloak: Keycloak | null;
  token: string | null;
  idToken: string | null;
  profile: KeycloakProfile | null;
  authenticated: boolean;
  initializing: boolean;
  hasRequiredRole: boolean;
  requiredRole: string;
  configOk: boolean;
  login: () => void;
  logout: () => void;
  getAuthHeader: () => Promise<string | null>;
};

const AuthContext = createContext<AuthContextType | null>(null);

const PUBLIC_URL = process.env.NEXT_PUBLIC_KEYCLOAK_URL;
const PUBLIC_REALM = process.env.NEXT_PUBLIC_KEYCLOAK_REALM;
const PUBLIC_CLIENT_ID = process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID;
const REQUIRED_ROLE = process.env.NEXT_PUBLIC_REQUIRED_ROLE || "one-mail-admin";

function createKeycloak(): Keycloak | null {
  if (!PUBLIC_URL || !PUBLIC_REALM || !PUBLIC_CLIENT_ID) return null;
  const config: KeycloakConfig = {
    url: PUBLIC_URL,
    realm: PUBLIC_REALM,
    clientId: PUBLIC_CLIENT_ID,
  };
  return new Keycloak(config);
}

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [keycloak, setKeycloak] = useState<Keycloak | null>(null);
  const [authenticated, setAuthenticated] = useState(false);
  const [initializing, setInitializing] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  const [idToken, setIdToken] = useState<string | null>(null);
  const [profile, setProfile] = useState<KeycloakProfile | null>(null);

  const refreshTimer = useRef<number | null>(null);

  const configOk = Boolean(PUBLIC_URL && PUBLIC_REALM && PUBLIC_CLIENT_ID);

  useEffect(() => {
    const kc = createKeycloak();
    setKeycloak(kc);
    if (!kc) {
      setInitializing(false);
      return;
    }

    kc.init({
      pkceMethod: "S256",
      checkLoginIframe: false,
      enableLogging: true,
    })
      .then(async (auth) => {
        setAuthenticated(auth);
        if (auth) {
          setToken(kc.token ?? null);
          setIdToken(kc.idToken ?? null);
          try {
            const prof = await kc.loadUserProfile();
            setProfile(prof);
          } catch {
            // ignore
          }
          scheduleRefresh(kc);
        }
      })
      .finally(() => setInitializing(false));

    return () => {
      if (refreshTimer.current) window.clearInterval(refreshTimer.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const scheduleRefresh = (kc: Keycloak) => {
    if (refreshTimer.current) window.clearInterval(refreshTimer.current);
    refreshTimer.current = window.setInterval(async () => {
      try {
        const refreshed = await kc.updateToken(30);
        if (refreshed) {
          setToken(kc.token ?? null);
          setIdToken(kc.idToken ?? null);
        }
      } catch {
        // token refresh failed
      }
    }, 30_000);
  };

  const login = useCallback(() => {
    keycloak?.login({
      redirectUri: `${window.location.origin}/auth/callback`,
    });
  }, [keycloak]);

  const logout = useCallback(() => {
    keycloak?.logout();
  }, [keycloak]);

  const roles = useMemo(() => {
    const realmRoles = (keycloak?.realmAccess?.roles ?? []) as string[];
    const clientRoles: string[] =
      keycloak?.resourceAccess?.[PUBLIC_CLIENT_ID || ""]?.roles ?? [];
    return new Set<string>([...realmRoles, ...clientRoles]);
  }, [keycloak]);

  const hasRequiredRole = roles.has(REQUIRED_ROLE);

  const getAuthHeader = useCallback(async () => {
    if (!keycloak) return null;
    try {
      await keycloak.updateToken(30);
      return keycloak.token ? `Bearer ${keycloak.token}` : null;
    } catch {
      return token ? `Bearer ${token}` : null;
    }
  }, [keycloak, token]);

  const value: AuthContextType = {
    keycloak,
    token,
    idToken,
    profile,
    authenticated,
    initializing,
    hasRequiredRole,
    requiredRole: REQUIRED_ROLE,
    configOk,
    login,
    logout,
    getAuthHeader,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error("useAuth must be used within AuthProvider");
  }
  return ctx;
}
