# Keycloak 代码实现指南

## 核心服务类 (app/lib/keycloak.ts)

```typescript
export interface KeycloakUser {
  id: string
  name: string
  email: string
  preferred_username: string
}

export class KeycloakService {
  private static readonly KEYCLOAK_URL = process.env.KEYCLOAK_URL!
  private static readonly REALM = process.env.KEYCLOAK_REALM!
  private static readonly CLIENT_ID = process.env.KEYCLOAK_CLIENT_ID!
  private static readonly CLIENT_SECRET = process.env.KEYCLOAK_CLIENT_SECRET!

  // 生成 PKCE 代码验证器
  private static generateCodeVerifier(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  // 生成 PKCE 代码挑战
  private static async generateCodeChallenge(verifier: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(verifier)
    const digest = await crypto.subtle.digest('SHA-256', data)
    return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(digest))))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  // 获取登录 URL
  static async getLoginUrl(redirectUri: string): Promise<{ url: string, codeVerifier: string }> {
    const codeVerifier = this.generateCodeVerifier()
    const codeChallenge = await this.generateCodeChallenge(codeVerifier)

    const params = new URLSearchParams({
      client_id: this.CLIENT_ID,
      redirect_uri: redirectUri,
      response_type: "code",
      scope: "openid profile email",
      code_challenge_method: "S256",
      code_challenge: codeChallenge,
    })

    return {
      url: `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/auth?${params}`,
      codeVerifier
    }
  }

  // 交换授权码获取令牌
  static async exchangeCodeForToken(code: string, redirectUri: string, codeVerifier: string) {
    const tokenUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/token`

    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
      }),
    })

    if (!response.ok) {
      throw new Error("Failed to exchange code for token")
    }

    return response.json()
  }

  // 获取用户信息
  static async getUserInfo(accessToken: string): Promise<KeycloakUser> {
    const userInfoUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/userinfo`

    const response = await fetch(userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error("Failed to get user info")
    }

    const userInfo = await response.json()

    return {
      id: userInfo.sub,
      name: userInfo.name || userInfo.preferred_username,
      email: userInfo.email,
      preferred_username: userInfo.preferred_username,
    }
  }

  // 登出
  static async logout(refreshToken: string): Promise<void> {
    const logoutUrl = `${this.KEYCLOAK_URL}/realms/${this.REALM}/protocol/openid-connect/logout`

    await fetch(logoutUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: this.CLIENT_ID,
        client_secret: this.CLIENT_SECRET,
        refresh_token: refreshToken,
      }),
    })
  }
}
```

## 认证上下文 (app/lib/auth-context.tsx)

```typescript
"use client"

import React, { createContext, useContext, useState, useEffect, useRef } from 'react'

interface User {
  id: string
  name: string
  email: string
  preferred_username: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const hasCheckedAuthRef = useRef(false)

  useEffect(() => {
    if (!hasCheckedAuthRef.current) {
      hasCheckedAuthRef.current = true
      checkAuth()
    }
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch("/api/auth/me")
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      } else if (response.status === 401) {
        setUser(null)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await fetch("/api/auth/logout", { method: "POST" })
      setUser(null)
      window.location.href = "/"
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  return (
    <AuthContext.Provider value={{ user, loading, logout }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
```

## API 路由实现

### 登录路由 (app/api/auth/login/route.ts)

```typescript
import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  // 强制使用环境变量，支持反向代理
  const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'
  const redirectUri = `${baseUrl}/api/auth/callback`

  // 生成 PKCE 参数
  const { url: loginUrl, codeVerifier } = await KeycloakService.getLoginUrl(redirectUri)

  // 在安全 cookie 中存储 code verifier
  const cookieStore = cookies()
  cookieStore.set("pkce_code_verifier", codeVerifier, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: 600, // 10 分钟
  })

  return NextResponse.redirect(loginUrl)
}
```

### 回调路由 (app/api/auth/callback/route.ts)

```typescript
import { type NextRequest, NextResponse } from "next/server"
import { KeycloakService } from "@/app/lib/keycloak"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get("code")
  const baseUrl = process.env.NEXTAUTH_URL || 'https://token-library.techexpresser.com'

  if (!code) {
    return NextResponse.redirect(`${baseUrl}/auth/signin?error=no_code`)
  }

  const cookieStore = cookies()
  const codeVerifierCookie = cookieStore.get("pkce_code_verifier")

  if (!codeVerifierCookie) {
    return NextResponse.redirect(`${baseUrl}/auth/signin?error=no_verifier`)
  }

  try {
    const redirectUri = `${baseUrl}/api/auth/callback`
    const tokenData = await KeycloakService.exchangeCodeForToken(code, redirectUri, codeVerifierCookie.value)
    const userInfo = await KeycloakService.getUserInfo(tokenData.access_token)

    // 清除 code verifier cookie
    cookieStore.delete("pkce_code_verifier")

    // 设置安全 cookies
    cookieStore.set("access_token", tokenData.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    cookieStore.set("refresh_token", tokenData.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.refresh_expires_in,
    })

    cookieStore.set("user_info", JSON.stringify(userInfo), {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      maxAge: tokenData.expires_in,
    })

    return NextResponse.redirect(`${baseUrl}/`)
  } catch (error) {
    console.error("Callback error:", error)
    return NextResponse.redirect(`${baseUrl}/auth/signin?error=callback_failed`)
  }
}
```

### 登出路由 (app/api/auth/logout/route.ts)

```typescript
import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import { KeycloakService } from "@/app/lib/keycloak"

export async function POST(request: NextRequest) {
  const cookieStore = cookies()
  const refreshToken = cookieStore.get("refresh_token")

  if (refreshToken) {
    try {
      await KeycloakService.logout(refreshToken.value)
    } catch (error) {
      console.error("Keycloak logout error:", error)
    }
  }

  // 清除所有认证 cookies
  cookieStore.delete("access_token")
  cookieStore.delete("refresh_token")
  cookieStore.delete("user_info")

  return NextResponse.json({ success: true })
}
```

### 用户信息路由 (app/api/auth/me/route.ts)

```typescript
import { type NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
  }

  try {
    const user = JSON.parse(userInfo.value)
    return NextResponse.json(user)
  } catch (error) {
    return NextResponse.json({ error: "Invalid user data" }, { status: 401 })
  }
}
```

## 中间件配置 (middleware.ts)

```typescript
import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  // 克隆请求头
  const requestHeaders = new Headers(request.headers)
  
  // 获取转发的主机信息
  const forwardedHost = request.headers.get('x-forwarded-host') || 
                       request.headers.get('x-original-host') ||
                       request.headers.get('host')
  
  // 获取转发的协议
  const forwardedProto = request.headers.get('x-forwarded-proto') || 
                        request.headers.get('x-forwarded-protocol') ||
                        (request.headers.get('x-forwarded-ssl') === 'on' ? 'https' : 'http')

  // 如果有转发头，更新请求
  if (forwardedHost && forwardedHost !== 'localhost:9032') {
    requestHeaders.set('host', forwardedHost)
  }

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
```

## API 路由保护模式

```typescript
// 在需要认证的 API 路由中使用
async function getAuthenticatedUser() {
  const cookieStore = cookies()
  const userInfo = cookieStore.get("user_info")

  if (!userInfo) {
    return null
  }

  try {
    return JSON.parse(userInfo.value)
  } catch {
    return null
  }
}

export async function POST(request: NextRequest) {
  const user = await getAuthenticatedUser()

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // 处理已认证的请求
  // ...
}
```

## 页面级保护

```typescript
// 在需要认证的页面组件中使用
function ProtectedPage() {
  const { user, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!user) {
    window.location.href = "/auth/signin"
    return null
  }

  return <div>Protected content for {user.name}</div>
}
```

## 登录页面组件 (app/auth/signin/page.tsx)

```typescript
"use client"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function SignInPage() {
  const handleLogin = () => {
    window.location.href = "/api/auth/login"
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Token Manager</CardTitle>
          <CardDescription>
            Secure token management with Keycloak authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button className="w-full" onClick={handleLogin}>
            Login with Keycloak
          </Button>
          <p className="text-sm text-gray-600 text-center">
            You will be redirected to Keycloak for secure authentication
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
```
