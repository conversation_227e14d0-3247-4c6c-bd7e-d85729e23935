# Keycloak 部署配置指南

## 环境变量配置

### 本地开发 (.env.local)
```env
# Keycloak 配置
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=dev
KEYCLOAK_CLIENT_ID=token-library-dev
KEYCLOAK_CLIENT_SECRET=WHm6ldIGhb0u02m3VCxZOjAxEjGMg23p
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/dev

# 应用配置
PORT=9032
NEXTAUTH_URL=http://localhost:9032
```

### 生产环境
```env
# Keycloak 配置
KEYCLOAK_URL=https://keycloak.techexpresser.com
KEYCLOAK_REALM=production
KEYCLOAK_CLIENT_ID=token-library-prod
KEYCLOAK_CLIENT_SECRET=<production-secret>
KEYCLOAK_ISSUER=https://keycloak.techexpresser.com/realms/production

# 应用配置
PORT=9032
NEXTAUTH_URL=https://token-library.techexpresser.com
NODE_ENV=production
```

## Keycloak 客户端配置

### 1. 创建客户端
- **Client ID**: `token-library-dev` (开发) / `token-library-prod` (生产)
- **Client Protocol**: `openid-connect`
- **Access Type**: `confidential`
- **Standard Flow**: `ON`
- **Direct Access Grants**: `OFF`

### 2. 重定向 URI 设置
**开发环境**:
```
Valid Redirect URIs:
- http://localhost:9032/*
- http://localhost:9032/api/auth/callback

Web Origins:
- http://localhost:9032
```

**生产环境**:
```
Valid Redirect URIs:
- https://token-library.techexpresser.com/*
- https://token-library.techexpresser.com/api/auth/callback

Web Origins:
- https://token-library.techexpresser.com
```

### 3. 高级设置
```
Access Token Lifespan: 5 Minutes
Client Session Idle: 30 Minutes
Proof Key for Code Exchange Code Challenge Method: S256
```

## 反向代理配置

### Nginx 配置
```nginx
server {
    listen 80;
    server_name token-library.techexpresser.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name token-library.techexpresser.com;

    # SSL 证书
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 代理配置
    location / {
        proxy_pass http://127.0.0.1:9032;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件缓存
    location /_next/static/ {
        proxy_pass http://127.0.0.1:9032;
        proxy_cache_valid 200 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Apache 配置
```apache
<VirtualHost *:80>
    ServerName token-library.techexpresser.com
    Redirect permanent / https://token-library.techexpresser.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName token-library.techexpresser.com
    
    # SSL 配置
    SSLEngine on
    SSLCertificateFile /path/to/ssl/certificate.crt
    SSLCertificateKeyFile /path/to/ssl/private.key
    
    # 代理配置
    ProxyPreserveHost On
    ProxyPass / http://127.0.0.1:9032/
    ProxyPassReverse / http://127.0.0.1:9032/
    
    # 头信息传递
    ProxyAddHeaders On
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-Host "token-library.techexpresser.com"
</VirtualHost>
```

## Next.js 配置 (next.config.mjs)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // 反向代理支持
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Forwarded-Host',
            value: 'token-library.techexpresser.com',
          },
        ],
      },
    ]
  },
}

export default nextConfig
```

## 部署步骤

### 本地开发
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.local.example .env.local
# 编辑 .env.local

# 3. 启动开发服务器
npm run dev
```

### 生产部署
```bash
# 1. 构建应用
npm run build

# 2. 使用 PM2 启动
npm install -g pm2
pm2 start npm --name "token-library" -- start
pm2 save
pm2 startup
```

### PM2 配置 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'token-library',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/token-library',
    env: {
      NODE_ENV: 'production',
      PORT: 9032
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

## 常见问题解决

### 1. 重定向 URI 不匹配
**错误**: `Invalid redirect_uri`
**解决**: 检查 Keycloak 客户端配置中的重定向 URI 是否包含正确的回调地址

### 2. PKCE 验证失败
**错误**: `PKCE verification failed`
**解决**: 检查 cookie 配置，确保 `sameSite: "lax"` 和正确的 `secure` 设置

### 3. Host 头信息错误
**错误**: 应用无法识别正确的域名
**解决**: 确保反向代理正确设置了 `X-Forwarded-Host` 和 `X-Forwarded-Proto` 头

### 4. 环境变量未加载
**错误**: `KEYCLOAK_URL is undefined`
**解决**: 检查 `.env.local` 文件格式，重启开发服务器

## 安全检查清单

### 开发环境
- [ ] `.env.local` 文件配置正确
- [ ] Keycloak 客户端重定向 URI 包含 `localhost:9032`
- [ ] Cookie `secure` 设置为 `false`

### 生产环境
- [ ] 环境变量通过安全方式设置（不在代码中）
- [ ] HTTPS 证书有效且配置正确
- [ ] 反向代理配置正确
- [ ] Cookie `secure` 设置为 `true`
- [ ] 安全头配置完整
- [ ] 防火墙规则配置

## 监控和维护

### 健康检查
```bash
# 检查应用状态
curl -f http://localhost:9032/api/auth/me

# 检查 Keycloak 连接
curl -f https://keycloak.techexpresser.com/realms/dev/.well-known/openid_configuration
```

### 日志查看
```bash
# PM2 日志
pm2 logs token-library

# Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 性能监控
```bash
# 系统资源
htop
free -h
df -h

# 进程状态
pm2 monit
```

## 快速命令参考

### 开发命令
```bash
npm install          # 安装依赖
npm run dev          # 启动开发服务器
npm run build        # 构建应用
npm start            # 启动生产服务器
```

### PM2 命令
```bash
pm2 start ecosystem.config.js    # 启动应用
pm2 status                       # 查看状态
pm2 logs token-library           # 查看日志
pm2 restart token-library        # 重启应用
pm2 stop token-library           # 停止应用
```

### Nginx 命令
```bash
nginx -t             # 测试配置
nginx -s reload      # 重新加载配置
systemctl restart nginx    # 重启服务
systemctl status nginx     # 查看状态
```

---

**重要提醒**: 
- 生产环境必须使用 HTTPS
- 定期更新 Keycloak 客户端密钥
- 监控应用日志和性能指标
- 备份重要配置文件
