# 邮件聚合应用需求文档

## 1. 项目目标

一个 **个人使用** 的邮件聚合 Web 应用，前端用 **Next.js**，后端通过 **IMAP** 从 Gmail 获取邮件，支持按收件人筛选，展示分页列表。  
登录方式是 **Keycloak 登录**（Login with Tech Expresser），并且只有具有特定角色（如 `mail-reader`）的账号才能使用。

切记 只是读取 email

---

## 2. 功能需求

### 登录与权限

- 前端使用 **Keycloak JS Adapter (keycloak-js)** 进行登录。
- 登录时跳转到 Keycloak Realm 认证页面，认证成功后返回 Token。
- 登录后检查用户 Token 是否包含指定 Role（例如 `mail-reader`），否则拒绝访问。
- 没有注册功能，账号只在 Keycloak 中手动创建。

### 邮件读取

- 使用 **IMAP 协议** 连接 Gmail（需启用 Gmail 的 IMAP 访问，并使用应用专用密码）。
- 拉取所有邮件，按时间倒序排列。
- 支持 **分页**（IMAP 原生不支持 offset-based pagination，需要用 UID 或 Sequence Number 反向拉取）。
- 支持根据 **收件人** 过滤（To、Cc、Bcc 以及转发头部中的 `Delivered-To`、`X-Forwarded-To`）。
- 邮件列表项展示：
  - 主题
  - 发件人
  - 收件人（包括转发过来的地址）
  - 时间
  - 是否已读
- 点击邮件可查看正文（HTML / 纯文本）和附件列表。

### 邮件来源

- 邮件包括：
  - 正常收到的 Gmail 邮件
  - Cloudflare Email Routing 转发过来的邮件（收件人可能是你自定义域名的邮箱）

---

## 3. 技术栈

### 前端

- **Next.js**（React + SSR）
- **Tailwind CSS**（UI 样式）
- **Keycloak JS Adapter**（用户登录）
- Axios / Fetch（调用后端 API 获取邮件）

### 后端

- nextjs
- `node-imap` 或 `imapflow`（IMAP 客户端库）
- `mailparser`（解析邮件内容、附件、收件人列表）
- PostgreSQL（可选，如果需要缓存邮件搜索结果）

---

## 4. IMAP 实现注意事项

1. **分页**

   - IMAP 没有 `LIMIT OFFSET`，只能用 `FETCH` 结合 UID 范围。
   - 一般做法：先用 `SEARCH` 获取所有 UID，然后反向 slice，分批 fetch。

2. **搜索效率**

   - Gmail 的 IMAP 搜索比较快，因为它是服务器端搜索，不会下载所有邮件再过滤。
   - 可以直接用 IMAP 命令：
     ```
     SEARCH TO "<EMAIL>" OR CC "<EMAIL>" OR BCC "<EMAIL>"
     ```
     再加上 `Delivered-To` 等字段需要先抓邮件头再过滤。

3. **转发邮件的收件人**
   - Cloudflare Email Routing 转发后，IMAP 默认 `To` 字段可能是原发件的地址。
   - 需要从邮件头部 (`headers`) 读取 `Delivered-To`、`X-Forwarded-To`，这些才是真正的路由目标地址。

---

## 5. 工作流程

1. 用户访问 `/login`，通过 Keycloak 登录，获取 Token。
2. 前端保存 Token 并在请求 `/api/mail/list` 时携带。
3. 后端验证 Token（Keycloak Public Key 验证 JWT），检查角色是否包含 `mail-reader`。
4. 通过 IMAP 连接 Gmail，执行搜索和分页，返回邮件列表 JSON。
5. 前端渲染邮件列表，支持翻页和收件人过滤。
6. 用户点击邮件进入 `/inbox/[uid]`，调用 `/api/mail/detail` 拉取并展示完整内容和附件。

---

## 6. 建议

- IMAP 部分建议用 **imapflow**，比 `node-imap` 更现代化，支持 async/await，分页实现更清晰。
- 由于目前是个人使用，数据库可以先不做，直接实时 IMAP 拉取；如果后续搜索量大，再加 PostgreSQL 缓存。
- 首次读取，先把 receiver list 先从 email 读取保存在 pg17，下次读取就对比
