import type { NextRequest } from "next/server"
import { type JWTPayload, createRemoteJWKSet, jwtVerify } from "jose"
import { getServerEnv } from "./config"

export type AuthResult = {
  sub: string
  email?: string
  roles: Set<string>
  token: string
  isDemo?: boolean
}

export async function verifyAuth(req: NextRequest): Promise<AuthResult> {
  const auth = req.headers.get("authorization") || ""
  const token = auth.startsWith("Bearer ") ? auth.slice(7) : null
  if (!token) {
    throw new Error("Missing Authorization header")
  }

  const { issuer, clientId, roleClientId } = getServerEnv()

  if (!issuer) {
    const err = new Error("Auth not configured (no issuer).")
      ; (err as any).status = 401
    throw err
  }

  const JWKS = createRemoteJWKSet(new URL(`${issuer}/protocol/openid-connect/certs`))
  const { payload } = await jwtVerify(token, JWKS, {
    issuer,
    audience: clientId ? [clientId] : undefined,
  })

  const roles = extractRoles(payload, roleClientId || undefined)

  return {
    sub: String(payload.sub),
    email: payload.email as string | undefined,
    roles,
    token,
    isDemo: false,
  }
}

function extractRoles(payload: JWTPayload, clientId?: string): Set<string> {
  const realmRoles = ((payload as any).realm_access?.roles || []) as string[]
  const clientRoles = clientId ? (((payload as any).resource_access?.[clientId]?.roles || []) as string[]) : []
  return new Set([...realmRoles, ...clientRoles])
}

export function ensureRole(auth: AuthResult, requiredRole: string) {
  if (!auth.roles.has(requiredRole)) {
    const err = new Error("Forbidden: missing required role")
      ; (err as any).status = 403
    throw err
  }
}
