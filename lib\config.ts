export function getServerEnv() {
  const requiredRole = process.env.KEYCLOAK_REQUIRED_ROLE || "one-mail-admin"
  // Prefer explicit issuer; else derive from public values (safe for server use)
  const issuer =
    process.env.KEYCLOAK_ISSUER_URL ||
    (process.env.NEXT_PUBLIC_KEYCLOAK_URL && process.env.NEXT_PUBLIC_KEYCLOAK_REALM
      ? `${process.env.NEXT_PUBLIC_KEYCLOAK_URL}/realms/${process.env.NEXT_PUBLIC_KEYCLOAK_REALM}`
      : undefined)

  const clientId = process.env.KEYCLOAK_CLIENT_ID || process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID
  const roleClientId = process.env.KEYCLOAK_ROLE_CLIENT_ID || clientId

  const imapHost = process.env.GMAIL_IMAP_HOST || "imap.gmail.com"
  const imapPort = Number(process.env.GMAIL_IMAP_PORT || "993")
  const imapUser = process.env.GMAIL_IMAP_USER
  const imapPass = process.env.GMAIL_IMAP_PASSWORD

  return {
    requiredRole,
    issuer,
    clientId,
    roleClientId,
    imap: {
      host: imapHost,
      port: imapPort,
      user: imapUser,
      pass: imapPass,
      secure: true as const,
    },
  }
}
