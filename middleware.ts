import { NextRequest, NextResponse } from "next/server"

export function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers)

  const forwardedHost =
    request.headers.get("x-forwarded-host") ||
    request.headers.get("x-original-host") ||
    request.headers.get("host")

  const forwardedProto =
    request.headers.get("x-forwarded-proto") ||
    request.headers.get("x-forwarded-protocol") ||
    (request.headers.get("x-forwarded-ssl") === "on" ? "https" : "http")

  if (forwardedHost && forwardedHost !== "localhost:9042") {
    requestHeaders.set("host", forwardedHost)
  }
  if (forwardedProto) {
    requestHeaders.set("x-forwarded-proto", forwardedProto)
  }

  return NextResponse.next({
    request: { headers: requestHeaders },
  })
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
}

