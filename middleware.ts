import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  // Clone the request headers
  const requestHeaders = new Headers(request.headers)
  
  // Get the forwarded host from various possible headers
  const forwardedHost = request.headers.get('x-forwarded-host') || 
                       request.headers.get('x-original-host') ||
                       request.headers.get('host')
  
  // Get the forwarded protocol
  const forwardedProto = request.headers.get('x-forwarded-proto') || 
                        request.headers.get('x-forwarded-protocol') ||
                        (request.headers.get('x-forwarded-ssl') === 'on' ? 'https' : 'http')

  // If we have forwarded headers, update the request
  if (forwardedHost && forwardedHost !== 'localhost:9032') {
    // Set the correct host header
    requestHeaders.set('host', forwardedHost)
    
    // Log for debugging
    console.log('=== MIDDLEWARE DEBUG ===')
    console.log('Original host:', request.headers.get('host'))
    console.log('Forwarded host:', forwardedHost)
    console.log('Forwarded proto:', forwardedProto)
    console.log('Request URL:', request.url)
    console.log('========================')
  }

  // Continue with the request
  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
